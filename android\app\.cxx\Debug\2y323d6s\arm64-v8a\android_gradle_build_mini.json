{"buildFiles": ["C:\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Educational\\personal\\projects\\collections\\android\\app\\.cxx\\Debug\\2y323d6s\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Educational\\personal\\projects\\collections\\android\\app\\.cxx\\Debug\\2y323d6s\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}